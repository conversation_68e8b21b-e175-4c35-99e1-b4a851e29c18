import tkinter as tk
from tkinter import messagebox
import pyperclip
import pygetwindow as gw
import pyautogui
import webbrowser
import re
import requests
from PIL import Image, ImageTk
from io import BytesIO
import time

TARGET_WINDOW_TITLE = "chrome"

class PasteApp:
    def __init__(self, root):
        self.root = root
        self.root.title("資訊解析器")
        self.root.attributes('-topmost', True)

        self.frame = tk.Frame(root)
        self.frame.pack(padx=10, pady=10)

        # 原有的文字輸入框，綁定點擊事件（自動貼上並解析）
        self.input_text = tk.Text(self.frame, height=10, width=60)
        self.input_text.pack()
        self.input_text.bind("<Button-1>", self.paste_and_parse)  # 綁定左鍵點擊事件

        # 新增圖片網址輸入框，綁定點擊事件
        self.image_url_label = tk.Label(self.frame, text="圖片網址：")
        self.image_url_label.pack()
        self.image_url_entry = tk.Entry(self.frame, width=60)
        self.image_url_entry.pack()
        self.image_url_entry.bind("<Button-1>", self.paste_clipboard_to_entry)  # 綁定左鍵點擊事件
        self.image_url_entry.bind("<KeyRelease>", self.update_image_preview)

        # 新增到貨日期輸入框
        self.arrival_date_label = tk.Label(self.frame, text="到貨日期（例：2024/07/31）：")
        self.arrival_date_label.pack()
        self.arrival_date_entry = tk.Entry(self.frame, width=60)
        self.arrival_date_entry.pack()

        # 圖片預覽區域
        self.image_preview_label = tk.Label(self.frame, text="圖片預覽：")
        self.image_preview_label.pack()
        self.image_canvas = tk.Label(self.frame)
        self.image_canvas.pack()

        # 新增三個按鈕：現貨、無庫存、預購（放大按鈕）
        self.button_frame = tk.Frame(self.frame)
        self.button_frame.pack(pady=5)
        self.stock_button = tk.Button(
            self.button_frame, 
            text="現貨", 
            command=self.copy_stock_text,
            font=("Arial", 14),  # 放大字體
            padx=15, pady=10  # 增加內邊距
        )
        self.stock_button.pack(side=tk.LEFT, padx=5)
        self.out_of_stock_button = tk.Button(
            self.button_frame, 
            text="無庫存", 
            command=self.copy_out_of_stock_text,
            font=("Arial", 14),  # 放大字體
            padx=15, pady=10  # 增加內邊距
        )
        self.out_of_stock_button.pack(side=tk.LEFT, padx=5)
        self.preorder_button = tk.Button(
            self.button_frame, 
            text="預購", 
            command=self.copy_preorder_text,
            font=("Arial", 14),  # 放大字體
            padx=15, pady=10  # 增加內邊距
        )
        self.preorder_button.pack(side=tk.LEFT, padx=5)

        # 解析結果顯示框架
        self.result_frame = tk.Frame(self.frame)
        self.result_frame.pack()

    def paste_and_parse(self, event):
        """點擊文字輸入框時自動貼上剪貼簿內容並解析"""
        try:
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                self.input_text.delete("1.0", tk.END)  # 清空現有內容
                self.input_text.insert("1.0", clipboard_text)  # 插入剪貼簿內容
                self.parse_and_display()  # 立即解析
        except Exception as e:
            messagebox.showerror("錯誤", f"無法貼上剪貼簿內容: {e}")
        return "break"  # 防止點擊事件影響文字框焦點

    def paste_clipboard_to_entry(self, event):
        """點擊圖片網址輸入框時自動貼上剪貼簿內容"""
        try:
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                self.image_url_entry.delete(0, tk.END)  # 清空現有內容
                self.image_url_entry.insert(0, clipboard_text)  # 插入剪貼簿內容
                self.update_image_preview()  # 立即更新圖片預覽
        except Exception as e:
            messagebox.showerror("錯誤", f"無法貼上剪貼簿內容: {e}")
        return "break"  # 防止點擊事件影響輸入框焦點

    def update_image_preview(self, event=None):
        """更新圖片預覽，縮放到原始大小的70%"""
        url = self.image_url_entry.get().strip()
        if url:
            try:
                response = requests.get(url)
                if response.status_code == 200:
                    img_data = response.content
                    img = Image.open(BytesIO(img_data))
                    # 計算原始大小的70%
                    original_width, original_height = img.size
                    new_width = int(original_width * 0.7)
                    new_height = int(original_height * 0.7)
                    img = img.resize((new_width, new_height), Image.LANCZOS)  # 縮放到70%
                    photo = ImageTk.PhotoImage(img)
                    self.image_canvas.configure(image=photo)
                    self.image_canvas.image = photo  # 保持引用
                else:
                    self.image_canvas.configure(image="", text="無法載入圖片")
            except Exception as e:
                self.image_canvas.configure(image="", text=f"載入圖片失敗: {e}")
        else:
            self.image_canvas.configure(image="", text="請輸入圖片網址")

    def parse_and_display(self):
        """解析輸入文字並顯示結果"""
        for widget in self.result_frame.winfo_children():
            widget.destroy()

        text = self.input_text.get("1.0", tk.END)
        data = self.parse_text(text)

        row = 0
        for item_text, raw_data in data:
            btn = tk.Button(
                self.result_frame,
                text=item_text,
                width=50,
                command=lambda d=raw_data: self.handle_click(d)
            )
            btn.grid(row=row, column=0, pady=2)
            row += 1

    def parse_text(self, text):
        """解析文字內容"""
        results = []
        urls = re.findall(r"https?://[^\s]+", text)
        if urls:
            results.append((f"網址（{len(urls)}）", urls))

        patterns = {
            "name": r"^4\.取貨姓名[:：]\s*(\S+)",
            "phone": r"^5\.手機\(不含\"-\"\)[:：]\s*(\d+)",
            "email": r"^6\.電子信箱[:：]\s*(\S+)",
            "store": r"^7\.門市店號/店名[:：]\s*([\w\s]*?)\s*(\d+)?\s*$"
        }

        for key, pattern in patterns.items():
            match = re.search(pattern, text, flags=re.MULTILINE)
            if match:
                if key == "store":
                    store_name = match.group(1).strip() if match.group(1) else ""
                    store_id = match.group(2).strip() if match.group(2) else ""
                    if store_id:
                        results.append((store_id, store_id))
                    if store_name:
                        results.append((store_name, store_name))
                else:
                    val = match.group(1)
                    if val:
                        val = val.strip()
                        if val != "":
                            results.append((val, val))

        return results

    def handle_click(self, data):
        """處理解析結果按鈕點擊"""
        if isinstance(data, list):
            try:
                wins = gw.getWindowsWithTitle(TARGET_WINDOW_TITLE)
                if wins:
                    wins[0].activate()
                    time.sleep(0.5)
                for url in data:
                    if url.strip().startswith("http"):
                        webbrowser.open(url.strip())
                        time.sleep(1)
            except Exception as e:
                messagebox.showerror("錯誤", f"開啟網址失敗: {e}")
        else:
            pyperclip.copy(data)
            try:
                wins = gw.getWindowsWithTitle(TARGET_WINDOW_TITLE)
                if wins:
                    wins[0].activate()
                    time.sleep(0.3)
                    pyautogui.hotkey("ctrl", "v")
            except Exception as e:
                messagebox.showerror("錯誤", f"切換視窗失敗: {e}")

    def copy_stock_text(self):
        """複製現貨文字"""
        image_url = self.image_url_entry.get().strip()
        if not image_url:
            messagebox.showerror("錯誤", "請輸入圖片網址")
            return
        text = (
            "您好：代訂已完成，商品送達門市會有e-mail通知(簡訊通知第四天才會發送)，謝謝您。\n\n"
            f"訂單明細：{image_url}"
        )
        pyperclip.copy(text)

    def copy_out_of_stock_text(self):
        """複製無庫存文字"""
        image_url = self.image_url_entry.get().strip()
        if not image_url:
            messagebox.showerror("錯誤", "請輸入圖片網址")
            return
        text = (
            "您好：代訂已完成，無庫存商品大約3~14個工作日左右出貨，調貨期間請耐心等待，"
            "商品送達門市會有e-mail通知(簡訊通知第四天才會發送)，謝謝您。\n\n"
            f"訂單明細：{image_url}"
        )
        pyperclip.copy(text)

    def copy_preorder_text(self):
        """複製預購文字"""
        image_url = self.image_url_entry.get().strip()
        arrival_date = self.arrival_date_entry.get().strip()
        if not image_url:
            messagebox.showerror("錯誤", "請輸入圖片網址")
            return
        if not arrival_date:
            messagebox.showerror("錯誤", "請輸入到貨日期")
            return
        text = (
            f"您好：代訂已完成，預售商品大約 {arrival_date} 左右出貨，預售期間請耐心等待，"
            "商品送達門市會有e-mail通知(簡訊通知第四天才會發送)，謝謝您。\n\n"
            f"訂單明細：{image_url}"
        )
        pyperclip.copy(text)

if __name__ == "__main__":
    root = tk.Tk()
    app = PasteApp(root)
    root.mainloop()