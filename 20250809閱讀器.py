import tkinter as tk
from tkinter import ttk, filedialog, messagebox, font
import fitz  # PyMuPDF
from PIL import Image, ImageTk
import io
import time

class ResizableTextBox:
    def __init__(self, canvas, x, y, width=200, height=100, proofreading_mode=None, move_distance_func=None, add_undo_record=None, auto_remove_spaces_func=None, page_ref=None):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.initial_x = x  # 保存初始X座標
        self.initial_y = y  # 保存初始Y座標
        self.page_ref = page_ref  # 頁面引用
        self.width = width
        self.height = height
        self.min_size = 20
        self.border_width = 1
        self.selected = False
        self.dragging = False
        self.resizing = False
        self.resize_direction = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.last_cursor_x = None
        self.last_cursor_y = None
        self.current_cursor = ''
        self.last_detected_cursor = ''
        self.hysteresis_buffer = 8
        self.last_motion_id = None
        self.proofreading_mode = proofreading_mode
        self.move_distance_func = move_distance_func
        self.add_undo_record = add_undo_record
        self.auto_remove_spaces_func = auto_remove_spaces_func
        self.external_buffer = 10
        self.current_font_family = "標楷體"
        self.current_font_size = 12
        self.red_char_index = None

        self.frame = tk.Frame(canvas, bg='white', relief='solid', bd=self.border_width)
        color = 'red' if proofreading_mode and proofreading_mode() else 'gray'
        self.frame.configure(highlightthickness=2, highlightcolor=color, highlightbackground=color, bd=0)

        self.text_widget = tk.Text(self.frame, wrap=tk.WORD, font=('標楷體', 12), bd=0)

        scrollbar = ttk.Scrollbar(self.frame, orient='vertical', command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)

        self.text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        self.window_id = canvas.create_window(x, y, window=self.frame, anchor='nw',
                                            width=width, height=height)

        self.frame.update_idletasks()
        self.width = self.frame.winfo_width() if self.frame.winfo_width() > 1 else width
        self.height = self.frame.winfo_height() if self.frame.winfo_height() > 1 else height
        self.canvas.itemconfig(self.window_id, width=self.width, height=self.height)

        self.frame.bind('<Button-1>', self.on_click)
        self.frame.bind('<B1-Motion>', self.on_drag)
        self.frame.bind('<ButtonRelease-1>', self.on_release)
        self.frame.bind('<Motion>', self.on_motion)
        self.frame.bind('<Enter>', self.on_enter)
        # 將文字框的右鍵事件轉發到畫布，並阻止事件繼續傳播
        def forward_right_click(event):
            self.canvas.event_generate('<Button-3>', x=event.x_root, y=event.y_root)

            def clear_focus_and_selection():
                self.canvas.focus_set()
                self.text_widget.tag_remove("sel", "1.0", "end")

            # 使用 'after' 來確保此操作在預設事件處理完成後運行
            self.canvas.after(1, clear_focus_and_selection)
            return "break"

        self.frame.bind('<Button-3>', forward_right_click)
        self.text_widget.bind('<Button-3>', forward_right_click)

        for widget in [self.frame, self.text_widget]:
            widget.bind('<Control-Button-1>', self.on_move_click)
            widget.bind('<Control-B1-Motion>', self.on_move_drag)
            widget.bind('<Control-ButtonRelease-1>', self.on_move_release)

        self.text_widget.bind('<Button-1>', self.on_text_click)
        self.text_widget.bind('<B1-Motion>', self.on_text_drag)
        self.text_widget.bind('<ButtonRelease-1>', self.on_text_release)
        self.text_widget.bind('<<Modified>>', self.on_text_modified)
        self.text_widget.bind('<<Paste>>', self.on_paste)
        self.text_widget.bind('<KeyPress>', self.on_key_press)

        # 綁定滾輪事件到文字框
        self.text_widget.bind('<MouseWheel>', self.on_text_mousewheel)
        self.text_widget.bind('<Button-4>', self.on_text_mousewheel)
        self.text_widget.bind('<Button-5>', self.on_text_mousewheel)

        self.frame.after(50, self.update_cursor)

        self.text_widget.tag_configure("red_char", foreground="red")
        self.text_widget.tag_configure("blue_char", foreground="blue")

        # 用於記錄文字修改前的狀態
        self.last_text_state = None
        self.text_change_timer = None

    def on_key_press(self, event):
        """記錄按鍵操作前的狀態，用於復原功能"""
        try:
            # 只記錄會修改文字的按鍵
            if event.keysym in ['BackSpace', 'Delete'] or (len(event.char) == 1 and event.char.isprintable()):
                if self.add_undo_record:
                    # 立即記錄當前狀態
                    current_text = self.text_widget.get("1.0", "end-1c")
                    current_x = self.x
                    current_y = self.y

                    # 保存藍色字符標記
                    blue_ranges = self.text_widget.tag_ranges("blue_char")
                    blue_char_info = []
                    for i in range(0, len(blue_ranges), 2):
                        start = str(blue_ranges[i])
                        end = str(blue_ranges[i + 1])
                        blue_char_info.append((start, end))

                    # 檢查是否需要記錄（避免重複記錄相同狀態）
                    if not hasattr(self, 'last_recorded_text') or self.last_recorded_text != current_text:
                        # 獲取當前頁面信息
                        current_page = None
                        if self.page_ref and hasattr(self.page_ref, 'current_page'):
                            current_page = self.page_ref.current_page

                        undo_record = {
                            'textbox': self,
                            'action': 'text_edit',
                            'original_text': current_text,
                            'original_x': current_x,
                            'original_y': current_y,
                            'initial_x': self.initial_x,  # 記錄真正的初始位置
                            'initial_y': self.initial_y,  # 記錄真正的初始位置
                            'width': self.width,
                            'height': self.height,
                            'font_size': self.current_font_size,
                            'red_char_index': self.red_char_index,
                            'blue_char_info': blue_char_info,
                            'page': current_page,  # 記錄頁面信息
                            'textbox_id': id(self)  # 記錄文字框的唯一標識
                        }
                        self.add_undo_record(undo_record)
                        self.last_recorded_text = current_text

        except Exception as e:
            print(f"按鍵記錄錯誤: {e}")

    def on_text_mousewheel(self, event):
        """處理文字框內的滾輪事件，根據文字框滾動開關決定是否允許滾動"""
        # 檢查是否有文字框滾動控制功能
        if (self.page_ref and hasattr(self.page_ref, 'textbox_scroll_enabled') and
            not self.page_ref.textbox_scroll_enabled):
            return "break"  # 禁用文字框內的滾動
        # 允許文字框內滾動
        return None




    def update_cursor(self):
        self.frame.config(cursor='')
        self.current_cursor = ''
        self.last_detected_cursor = ''

    def on_enter(self, event):
        cursor = self.get_cursor_for_position(event.x, event.y)
        if cursor != self.current_cursor:
            self.frame.config(cursor=cursor)
            self.current_cursor = cursor
            self.last_detected_cursor = cursor

    def on_motion(self, event):
        if not self.resizing and not self.dragging:
            if self.last_motion_id is not None:
                try:
                    self.canvas.after_cancel(self.last_motion_id)
                except tk.TclError:
                    pass
            self.last_motion_id = self.canvas.after(10, lambda: self.update_motion(event.x, event.y))

    def update_motion(self, x, y):
        self.last_cursor_x = x
        self.last_cursor_y = y
        cursor = self.get_cursor_for_position(x, y)
        if cursor != self.current_cursor:
            self.frame.config(cursor=cursor)
            self.current_cursor = cursor
            self.last_detected_cursor = cursor if cursor else self.last_detected_cursor
        elif not cursor and not self.is_in_hysteresis_buffer(x, y):
            self.frame.config(cursor='')
            self.current_cursor = ''
        self.last_motion_id = None

    def is_in_hysteresis_buffer(self, x, y):
        if not self.last_detected_cursor:
            return False
        buffer = self.hysteresis_buffer
        edge_width_x = 25
        edge_width_y = 35
        corner_size_x = 20
        corner_size_y = 10
        external_buffer = self.external_buffer

        if self.last_detected_cursor in ('size_nw_se', 'size_ne_sw'):
            if ((x >= -external_buffer - buffer and x <= corner_size_x + buffer and
                 y >= -external_buffer - buffer and y <= corner_size_y + buffer) or
                (x >= self.width - corner_size_x - buffer and x <= self.width + external_buffer + buffer and
                 y >= self.height - corner_size_y - buffer and y <= self.height + external_buffer + buffer) or
                (x >= self.width - corner_size_x - buffer and x <= self.width + external_buffer + buffer and
                 y >= -external_buffer - buffer and y <= corner_size_y + buffer) or
                (x >= -external_buffer - buffer and x <= corner_size_x + buffer and
                 y >= self.height - corner_size_y - buffer and y <= self.height + external_buffer + buffer)):
                return True
        elif self.last_detected_cursor == 'size_we':
            if ((x >= -external_buffer - buffer and x <= edge_width_x + buffer and
                 corner_size_y - buffer < y < self.height - corner_size_y + buffer) or
                (x >= self.width - edge_width_x - buffer and x <= self.width + external_buffer + buffer and
                 corner_size_y - buffer < y < self.height - corner_size_y + buffer)):
                return True
        elif self.last_detected_cursor == 'size_ns':
            if ((y >= -external_buffer - buffer and y <= edge_width_y + buffer and
                 corner_size_x - buffer < x < self.width - corner_size_x + buffer) or
                (y >= self.height - edge_width_y - buffer and y <= self.height + external_buffer + buffer and
                 corner_size_x - buffer < x < self.width - corner_size_x + buffer)):
                return True
        return False

    def get_cursor_for_position(self, x, y):
        edge_width_x = 25
        edge_width_y = 35
        corner_size_x = 20
        corner_size_y = 10
        external_buffer = self.external_buffer

        if ((x >= -external_buffer and x <= edge_width_x and corner_size_y < y < self.height - corner_size_y) or
            (x >= self.width - edge_width_x and x <= self.width + external_buffer and corner_size_y < y < self.height - corner_size_y)):
            return 'size_we'
        if ((y >= -external_buffer and y <= edge_width_y and corner_size_x < x < self.width - corner_size_x) or
            (y >= self.height - edge_width_y and y <= self.height + external_buffer and corner_size_x < x < self.width - corner_size_x)):
            return 'size_ns'

        if ((x >= -external_buffer and x <= corner_size_x and y >= -external_buffer and y <= corner_size_y) or
            (x >= self.width - corner_size_x and x <= self.width + external_buffer and
             y >= self.height - corner_size_y and y <= self.height + external_buffer)):
            return 'size_nw_se'
        if ((x >= self.width - corner_size_x and x <= self.width + external_buffer and y >= -external_buffer and y <= corner_size_y) or
            (x >= -external_buffer and x <= corner_size_x and y >= self.height - corner_size_y and y <= self.height + external_buffer)):
            return 'size_ne_sw'

        if self.is_in_hysteresis_buffer(x, y):
            return self.last_detected_cursor
        return ''

    def on_click(self, event):
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.resize_direction = self.get_resize_direction(event.x, event.y)
        if self.resize_direction:
            self.resizing = True
        return "break"

    def on_move_click(self, event):
        self.selected = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.dragging = True
        # 記錄拖拽開始時的位置，用於復原記錄
        self.drag_start_pos_x = self.x
        self.drag_start_pos_y = self.y
        return "break"

    def on_move_drag(self, event):
        if self.dragging:
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            self.x += dx
            self.y += dy
            self.canvas.coords(self.window_id, self.x, self.y)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root

            # 根據滾動跟隨開關決定是否執行邊界滾動跟隨
            if self.page_ref and hasattr(self.page_ref, 'scroll_follow_enabled') and self.page_ref.scroll_follow_enabled:
                self.boundary_scroll_follow()
        return "break"

    def on_move_release(self, event):
        if self.dragging:
            # 檢查位置是否有變化
            if (hasattr(self, 'drag_start_pos_x') and hasattr(self, 'drag_start_pos_y') and
                (abs(self.x - self.drag_start_pos_x) > 1 or abs(self.y - self.drag_start_pos_y) > 1)):

                # 記錄文字框移動操作
                if self.add_undo_record:
                    undo_record = {
                        'textbox': self,
                        'action': 'textbox_move',
                        'old_x': self.drag_start_pos_x,
                        'old_y': self.drag_start_pos_y,
                        'new_x': self.x,
                        'new_y': self.y,
                        'textbox_text': self.text_widget.get("1.0", "end-1c"),
                        'textbox_font_size': self.current_font_size,
                        'textbox_red_char_index': self.red_char_index
                    }
                    self.add_undo_record(undo_record)

        self.dragging = False
        return "break"

    def on_text_click(self, event):
        if self.proofreading_mode and self.proofreading_mode():
            try:
                click_index = self.text_widget.index(f"@{event.x},{event.y}")
                click_index_plus_one = self.text_widget.index(f"{click_index}+1c")

                # 檢查刪除位置後面是否有空格或換行符號
                delete_end_index = click_index_plus_one
                remaining_text = self.text_widget.get(click_index_plus_one, "end-1c")
                should_move_textbox = True  # 預設會移動文字框

                if remaining_text and remaining_text[0] == ' ':
                    # 如果後面有空格，也一併刪除，但不移動文字框
                    delete_end_index = self.text_widget.index(f"{click_index_plus_one}+1c")
                    should_move_textbox = False
                elif remaining_text and remaining_text[0] == '\n':
                    # 如果後面有換行符號，也一併刪除
                    delete_end_index = self.text_widget.index(f"{click_index_plus_one}+1c")
                    should_move_textbox = True  # 刪除換行符號時仍然移動文字框

                if self.add_undo_record:
                    original_text = self.text_widget.get("1.0", "end-1c")
                    original_x = self.x
                    original_y = self.y
                    deleted_text = self.text_widget.get("1.0", delete_end_index)
                    remaining_text_after_delete = self.text_widget.get(delete_end_index, "end-1c")

                    # 保存藍色字符標記
                    blue_ranges = self.text_widget.tag_ranges("blue_char")
                    blue_char_info = []
                    for i in range(0, len(blue_ranges), 2):
                        start = str(blue_ranges[i])
                        end = str(blue_ranges[i + 1])
                        blue_char_info.append((start, end))

                    undo_record = {
                        'textbox': self,
                        'action': 'proofreading_delete',
                        'original_text': original_text,
                        'original_x': original_x,
                        'original_y': original_y,
                        'initial_x': self.initial_x,  # 記錄真正的初始位置
                        'initial_y': self.initial_y,  # 記錄真正的初始位置
                        'deleted_text': deleted_text,
                        'remaining_text': remaining_text_after_delete,
                        'click_index': delete_end_index,
                        'red_char_index': self.red_char_index,
                        'blue_char_info': blue_char_info
                    }
                    self.add_undo_record(undo_record)

                self.text_widget.delete("1.0", delete_end_index)

                # 只有在沒有刪除空格的情況下才移動文字框
                if should_move_textbox:
                    # 校對刪除時強制執行滾動跟隨，不受滾動跟隨開關影響
                    self.move_textbox_down(force_scroll_follow=True)

                self.update_red_char()

                # 關鍵：在刪除文字並完成所有操作後，立即清除任何可能產生的選取狀態
                self.text_widget.tag_remove("sel", "1.0", "end")
                return "break"
            except Exception as e:
                print(f"校對模式錯誤: {e}")
        return None

    def on_text_drag(self, event):
        """處理文字框內的拖拽事件"""
        return None

    def on_text_release(self, event):
        """處理文字框內的釋放事件"""
        return None





    def move_textbox_down(self, force_scroll_follow=False):
        try:
            move_distance = self.get_move_distance()
            self.y += move_distance
            self.canvas.coords(self.window_id, self.x, self.y)

            # 根據滾動跟隨開關或強制跟隨參數決定是否執行畫布滾動跟隨
            should_scroll_follow = force_scroll_follow or (
                self.page_ref and
                hasattr(self.page_ref, 'scroll_follow_enabled') and
                self.page_ref.scroll_follow_enabled
            )

            if should_scroll_follow:
                # 在校對模式下使用同步滾動，保持視覺位置不變
                if self.proofreading_mode and self.proofreading_mode():
                    self.sync_scroll_with_movement(move_distance)
                else:
                    # 非校對模式使用新的邊界滾動跟隨
                    self.boundary_scroll_follow()
        except Exception as e:
            print(f"移動文字框錯誤: {e}")

    def get_move_distance(self):
        if self.move_distance_func:
            return self.move_distance_func()
        return 20

    def restore_state(self, text, x, y, red_char_index=None, blue_char_info=None):
        try:
            # 檢查文字框是否仍然存在
            if not hasattr(self, 'text_widget') or not self.text_widget.winfo_exists():
                print("文字框已被銷毀，無法恢復狀態")
                return False

            self.text_widget.delete("1.0", "end")
            self.text_widget.insert("1.0", text)
            self.x = x
            self.y = y

            # 檢查canvas和window_id是否仍然有效
            if hasattr(self, 'canvas') and hasattr(self, 'window_id'):
                try:
                    self.canvas.coords(self.window_id, x, y)
                except tk.TclError:
                    print("無法更新文字框位置，可能已被銷毀")


            self.red_char_index = red_char_index
            self.update_red_char()

            # 恢復藍色字符標記
            if blue_char_info:
                for start_pos, end_pos in blue_char_info:
                    try:
                        self.text_widget.tag_add("blue_char", start_pos, end_pos)
                    except tk.TclError:
                        # 如果位置無效，跳過這個標記
                        pass
            return True
        except Exception as e:
            print(f"恢復狀態錯誤: {e}")
            return False

    def on_drag(self, event):
        if self.resizing and self.resize_direction:
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y
            new_width = self.width
            new_height = self.height
            new_x = self.x
            new_y = self.y
            if 'left' in self.resize_direction:
                new_width = max(self.min_size, self.width - dx)
                new_x = self.x + dx
            elif 'right' in self.resize_direction:
                new_width = max(self.min_size, self.width + dx)
            if 'top' in self.resize_direction:
                new_height = max(self.min_size, self.height - dy)
                new_y = self.y + dy
            elif 'bottom' in self.resize_direction:
                new_height = max(self.min_size, self.height + dy)

            self.width = new_width
            self.height = new_height
            self.x = new_x
            self.y = new_y
            self.canvas.coords(self.window_id, self.x, self.y)
            self.canvas.itemconfig(self.window_id, width=self.width, height=self.height)
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root
        return "break"

    def on_release(self, event):
        self.dragging = False
        self.resizing = False
        self.resize_direction = None

    def get_resize_direction(self, x, y):
        edge_width_x = 25
        edge_width_y = 35
        external_buffer = self.external_buffer
        direction = []
        if y >= -external_buffer and y <= edge_width_y:
            direction.append('top')
        elif y >= self.height - edge_width_y and y <= self.height + external_buffer:
            direction.append('bottom')
        if x >= -external_buffer and x <= edge_width_x:
            direction.append('left')
        elif x >= self.width - edge_width_x and x <= self.width + external_buffer:
            direction.append('right')
        return '_'.join(direction) if direction else None

    def set_font(self, font_family, font_size):
        try:
            if isinstance(font_size, str):
                font_size = float(font_size)
            new_font = font.Font(family="標楷體", size=int(font_size))
            self.text_widget.config(font=new_font)
            self.current_font_family = "標楷體"
            self.current_font_size = font_size
            self.update_red_char()
        except Exception as e:
            print(f"字體設定錯誤: {e}")
            try:
                default_font = font.Font(family="標楷體", size=12)
                self.text_widget.config(font=default_font)
            except:
                pass

    def set_red_char(self, index):
        try:
            old_index = self.red_char_index
            self.red_char_index = index
            if self.add_undo_record:
                undo_record = {
                    'textbox': self,
                    'action': 'set_red_char',
                    'old_red_char_index': old_index,
                    'new_red_char_index': index
                }
                self.add_undo_record(undo_record)
            self.update_red_char()
        except Exception as e:
            print(f"設置紅色字錯誤: {e}")

    def update_red_char(self):
        try:
            self.text_widget.tag_remove("red_char", "1.0", "end")
            if self.red_char_index is not None:
                text = self.text_widget.get("1.0", "end-1c")
                if self.red_char_index >= 0 and self.red_char_index < len(text):
                    start_index = f"1.0 + {self.red_char_index} chars"
                    end_index = f"1.0 + {self.red_char_index + 1} chars"
                    self.text_widget.tag_add("red_char", start_index, end_index)
        except Exception as e:
            print(f"更新紅色字錯誤: {e}")

    def preserve_blue_chars(self):
        """保持藍色字符標記在文字修改後不消失"""
        try:
            # 這個方法可以在需要時調用來重新應用藍色標記
            # 目前藍色標記會在貼上時自動添加，並且會保持到手動清除
            pass
        except Exception as e:
            print(f"保持藍色字錯誤: {e}")

    def on_paste(self, event):
        """處理貼上事件：根據設置決定是否移除空格並將空格前的字標記為藍色"""
        try:
            # 檢查是否啟用自動移除空格功能
            if not (self.auto_remove_spaces_func and self.auto_remove_spaces_func()):
                # 如果功能關閉，使用默認貼上行為
                return None

            # 獲取剪貼簿內容
            clipboard_text = self.text_widget.clipboard_get()

            # 如果剪貼簿為空或只有空白字符，使用默認行為
            if not clipboard_text or not clipboard_text.strip():
                return None

            # 處理文字：移除空格並記錄空格前的字符位置
            processed_text = ""
            blue_char_positions = []

            for i, char in enumerate(clipboard_text):
                if char == ' ':
                    # 遇到空格時，記錄前一個字符的位置（如果存在）
                    if processed_text:  # 確保不是開頭的空格
                        blue_char_positions.append(len(processed_text) - 1)
                    # 不添加空格到處理後的文字中
                else:
                    processed_text += char

            # 如果沒有空格，使用默認貼上行為
            if not blue_char_positions:
                return None

            # 獲取當前插入位置
            insert_pos = self.text_widget.index(tk.INSERT)

            # 插入處理後的文字
            self.text_widget.insert(insert_pos, processed_text)

            # 標記藍色字符
            if blue_char_positions:
                for relative_pos in blue_char_positions:
                    # 使用 tkinter 的字符索引方式
                    start_index = f"{insert_pos} + {relative_pos} chars"
                    end_index = f"{insert_pos} + {relative_pos + 1} chars"

                    # 添加藍色標記
                    self.text_widget.tag_add("blue_char", start_index, end_index)

            # 阻止默認的貼上行為
            return "break"

        except tk.TclError:
            # 剪貼簿不存在或無法訪問，使用默認行為
            return None
        except Exception as e:
            print(f"貼上處理錯誤: {e}")
            # 如果出錯，允許默認貼上行為
            return None

    def on_text_modified(self, event):
        if self.text_widget.edit_modified():
            # 保存當前的藍色標記
            blue_ranges = self.text_widget.tag_ranges("blue_char")

            self.update_red_char()

            # 恢復藍色標記
            if blue_ranges:
                for i in range(0, len(blue_ranges), 2):
                    start = blue_ranges[i]
                    end = blue_ranges[i + 1]
                    self.text_widget.tag_add("blue_char", start, end)

            self.text_widget.edit_modified(False)

    def set_border_color(self, color):
        self.frame.configure(highlightcolor=color, highlightbackground=color)

    def reset_to_initial_position(self):
        """重置文字框位置到初始座標"""
        try:
            self.x = self.initial_x
            self.y = self.initial_y
            self.canvas.coords(self.window_id, self.x, self.y)
        except Exception as e:
            print(f"重置文字框位置錯誤: {e}")

    def reset_to_initial_y(self):
        """重置文字框Y座標到初始位置，保持X座標不變"""
        try:
            self.y = self.initial_y
            self.canvas.coords(self.window_id, self.x, self.y)
        except Exception as e:
            print(f"重置文字框Y座標錯誤: {e}")



    def ensure_visible_with_smooth_scroll(self):
        """確保文字框在可視區域內，並平滑滾動到合適位置"""
        try:
            # 取得畫布的可視區域和整體區域
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 使用畫布的 scrollregion 配置而不是 bbox("all")
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            # 解析 scrollregion 字符串 "x1 y1 x2 y2"
            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
            except (ValueError, AttributeError):
                # 如果解析失敗，回退到使用 bbox
                scroll_region = self.canvas.bbox("all")
                if not scroll_region:
                    return
                total_width = scroll_region[2] - scroll_region[0]
                total_height = scroll_region[3] - scroll_region[1]

            # 計算文字方塊的可視範圍
            box_visible_left = self.x
            box_visible_right = self.x + self.width
            box_visible_top = self.y
            box_visible_bottom = self.y + self.height

            # 取得當前卷軸位置
            current_x = self.canvas.xview()[0]
            current_y = self.canvas.yview()[0]

            # 計算目標卷軸位置，讓文字框保持在畫面中央區域
            # 設定中央區域的範圍（畫面的中央60%區域）
            center_margin_x = canvas_width * 0.2  # 左右各留20%
            center_margin_y = canvas_height * 0.2  # 上下各留20%

            smooth_factor = 0.15  # 溫和的滾動速度

            # 計算當前可視區域
            visible_left = current_x * total_width
            visible_right = visible_left + canvas_width
            visible_top = current_y * total_height
            visible_bottom = visible_top + canvas_height

            # 計算中央區域邊界
            center_left = visible_left + center_margin_x
            center_right = visible_right - center_margin_x
            center_top = visible_top + center_margin_y
            center_bottom = visible_bottom - center_margin_y

            # 水平方向：當文字框離開中央區域時開始滾動
            if box_visible_right > center_right:
                # 文字框右側超出中央區域，向右滾動
                target_x = (box_visible_right - canvas_width + center_margin_x) / total_width
                target_x = min(1, max(0, target_x))
                new_x = current_x + (target_x - current_x) * smooth_factor
                self.canvas.xview_moveto(new_x)
            elif box_visible_left < center_left:
                # 文字框左側超出中央區域，向左滾動
                target_x = (box_visible_left - center_margin_x) / total_width
                target_x = min(1, max(0, target_x))
                new_x = current_x + (target_x - current_x) * smooth_factor
                self.canvas.xview_moveto(new_x)

            # 垂直方向：當文字框離開中央區域時開始滾動
            if box_visible_bottom > center_bottom:
                # 文字框底部超出中央區域，向下滾動
                target_y = (box_visible_bottom - canvas_height + center_margin_y) / total_height
                target_y = min(1, max(0, target_y))
                new_y = current_y + (target_y - current_y) * smooth_factor
                self.canvas.yview_moveto(new_y)
            elif box_visible_top < center_top:
                # 文字框頂部超出中央區域，向上滾動
                target_y = (box_visible_top - center_margin_y) / total_height
                target_y = min(1, max(0, target_y))
                new_y = current_y + (target_y - current_y) * smooth_factor
                self.canvas.yview_moveto(new_y)

        except Exception as e:
            print(f"滾動跟隨錯誤: {e}")

    def sync_scroll_with_movement(self, move_distance):
        """與文字框移動同步的滾動，保持視覺位置不變"""
        try:
            # 使用畫布的 scrollregion 配置
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            # 解析 scrollregion 字符串 "x1 y1 x2 y2"
            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_height = y2 - y1
            except (ValueError, AttributeError):
                # 如果解析失敗，回退到使用 bbox
                scroll_region = self.canvas.bbox("all")
                if not scroll_region:
                    return
                total_height = scroll_region[3] - scroll_region[1]

            # 計算當前垂直滾動位置
            current_y = self.canvas.yview()[0]

            # 計算需要同步移動的滾動距離
            # move_distance 是文字框移動的像素距離
            # 需要轉換為滾動比例
            scroll_distance_ratio = move_distance / total_height

            # 計算新的滾動位置
            new_scroll_y = current_y + scroll_distance_ratio

            # 限制滾動範圍在 0.0 到 1.0 之間
            new_scroll_y = max(0.0, min(1.0, new_scroll_y))

            # 執行同步滾動
            self.canvas.yview_moveto(new_scroll_y)

        except Exception as e:
            print(f"同步滾動錯誤: {e}")

    def realtime_scroll_follow(self):
        """實時滾動跟隨，讓卷軸與文字框移動完全同步"""
        try:
            # 取得畫布的可視區域和整體區域
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 使用畫布的 scrollregion 配置
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            # 解析 scrollregion 字符串 "x1 y1 x2 y2"
            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
            except (ValueError, AttributeError):
                # 如果解析失敗，回退到使用 bbox
                scroll_region = self.canvas.bbox("all")
                if not scroll_region:
                    return
                total_width = scroll_region[2] - scroll_region[0]
                total_height = scroll_region[3] - scroll_region[1]

            # 計算文字框中心位置
            textbox_center_x = self.x + self.width / 2
            textbox_center_y = self.y + self.height / 2

            # 計算目標滾動位置，讓文字框中心保持在畫面中心
            # 水平方向
            if total_width > canvas_width:
                # 計算讓文字框中心在畫面中心的滾動位置
                target_center_x = canvas_width / 2
                target_scroll_x = (textbox_center_x - target_center_x) / (total_width - canvas_width)
                target_scroll_x = max(0.0, min(1.0, target_scroll_x))

                # 直接設置滾動位置，不使用漸進式滾動
                self.canvas.xview_moveto(target_scroll_x)

            # 垂直方向
            if total_height > canvas_height:
                # 計算讓文字框中心在畫面中心的滾動位置
                target_center_y = canvas_height / 2
                target_scroll_y = (textbox_center_y - target_center_y) / (total_height - canvas_height)
                target_scroll_y = max(0.0, min(1.0, target_scroll_y))

                # 直接設置滾動位置，不使用漸進式滾動
                self.canvas.yview_moveto(target_scroll_y)

        except Exception as e:
            print(f"實時滾動跟隨錯誤: {e}")

    def simple_scroll_follow(self):
        """簡單的滾動跟隨，基於文字框移動距離直接調整滾動"""
        try:
            # 檢查是否有記錄的上一個位置
            if not hasattr(self, '_last_textbox_x'):
                self._last_textbox_x = self.x
                self._last_textbox_y = self.y
                return

            # 計算文字框移動的距離
            dx = self.x - self._last_textbox_x
            dy = self.y - self._last_textbox_y

            # 更新記錄的位置
            self._last_textbox_x = self.x
            self._last_textbox_y = self.y

            # 如果移動距離太小，不進行滾動
            if abs(dx) < 1 and abs(dy) < 1:
                return

            # 獲取滾動區域信息
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
            except (ValueError, AttributeError):
                return

            # 獲取畫布大小
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 獲取當前滾動位置
            current_scroll_x = self.canvas.xview()[0]
            current_scroll_y = self.canvas.yview()[0]

            # 計算滾動調整量（按比例）
            if total_width > canvas_width:
                scroll_dx = dx / (total_width - canvas_width)
                new_scroll_x = current_scroll_x + scroll_dx
                new_scroll_x = max(0.0, min(1.0, new_scroll_x))
                self.canvas.xview_moveto(new_scroll_x)

            if total_height > canvas_height:
                scroll_dy = dy / (total_height - canvas_height)
                new_scroll_y = current_scroll_y + scroll_dy
                new_scroll_y = max(0.0, min(1.0, new_scroll_y))
                self.canvas.yview_moveto(new_scroll_y)

        except Exception as e:
            print(f"簡單滾動跟隨錯誤: {e}")

    def boundary_scroll_follow(self):
        """邊界滾動跟隨：只有當文字框移出可視區域時才滾動"""
        try:
            # 取得畫布的可視區域
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 取得當前滾動位置
            current_scroll_x = self.canvas.xview()[0]
            current_scroll_y = self.canvas.yview()[0]

            # 使用畫布的 scrollregion 配置
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            # 解析 scrollregion 字符串 "x1 y1 x2 y2"
            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
            except (ValueError, AttributeError):
                # 如果解析失敗，回退到使用 bbox
                scroll_region = self.canvas.bbox("all")
                if not scroll_region:
                    return
                total_width = scroll_region[2] - scroll_region[0]
                total_height = scroll_region[3] - scroll_region[1]

            # 計算當前可視區域的絕對座標
            visible_left = current_scroll_x * total_width
            visible_right = visible_left + canvas_width
            visible_top = current_scroll_y * total_height
            visible_bottom = visible_top + canvas_height

            # 計算文字框的邊界
            textbox_left = self.x
            textbox_right = self.x + self.width
            textbox_top = self.y
            textbox_bottom = self.y + self.height

            # 設定邊界緩衝區（當文字框接近邊界時開始滾動）
            buffer_margin = 50  # 50像素的緩衝區

            # 檢查是否需要水平滾動
            need_scroll_x = False
            target_scroll_x = current_scroll_x

            if textbox_right > visible_right - buffer_margin:
                # 文字框右邊界接近或超出可視區域右邊界
                target_scroll_x = (textbox_right + buffer_margin - canvas_width) / total_width
                need_scroll_x = True
            elif textbox_left < visible_left + buffer_margin:
                # 文字框左邊界接近或超出可視區域左邊界
                target_scroll_x = (textbox_left - buffer_margin) / total_width
                need_scroll_x = True

            # 檢查是否需要垂直滾動
            need_scroll_y = False
            target_scroll_y = current_scroll_y

            if textbox_bottom > visible_bottom - buffer_margin:
                # 文字框下邊界接近或超出可視區域下邊界
                target_scroll_y = (textbox_bottom + buffer_margin - canvas_height) / total_height
                need_scroll_y = True
            elif textbox_top < visible_top + buffer_margin:
                # 文字框上邊界接近或超出可視區域上邊界
                target_scroll_y = (textbox_top - buffer_margin) / total_height
                need_scroll_y = True

            # 限制滾動範圍
            target_scroll_x = max(0.0, min(1.0, target_scroll_x))
            target_scroll_y = max(0.0, min(1.0, target_scroll_y))

            # 只有在需要時才執行滾動，並使用平滑滾動
            smooth_factor = 0.3  # 較快的滾動速度，避免延遲

            if need_scroll_x:
                new_scroll_x = current_scroll_x + (target_scroll_x - current_scroll_x) * smooth_factor
                self.canvas.xview_moveto(new_scroll_x)

            if need_scroll_y:
                new_scroll_y = current_scroll_y + (target_scroll_y - current_scroll_y) * smooth_factor
                self.canvas.yview_moveto(new_scroll_y)

        except Exception as e:
            print(f"邊界滾動跟隨錯誤: {e}")

class PDFReader:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF閱讀器")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # 預設全螢幕開啟

        self.pdf_document = None
        self.current_page = 0
        self.zoom_level = 1.0
        # 改為全局單一文字框系統
        self.global_textbox = None  # 全局唯一文字框
        self.textbox_page = 0  # 文字框當前所在的頁面
        self.proofreading_mode = False
        self.auto_remove_spaces = True  # 控制貼上時是否自動移除空格的功能
        self.scroll_follow_enabled = True  # 控制文字框移動時是否跟隨滾動
        self.textbox_scroll_enabled = False  # 控制文字框內是否可以滾動

        self.undo_history = []
        self.redo_history = []
        self.max_undo_history = 50
        self.screen_dpi = root.winfo_fpixels('1i')
        self.font_size_var = tk.StringVar(value="12")
        self.font_size_var2 = tk.StringVar(value="16")
        self.active_font_var = self.font_size_var  # 追蹤當前使用的字體大小

        # 記錄頁面的滾動位置（不再記錄文字框位置，因為只有一個全局文字框）
        self.page_scroll_positions = {}  # {page_number: {'scroll_x': x, 'scroll_y': y}}

        self.setup_ui()

    def setup_ui(self):

        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="開啟PDF", command=self.open_pdf).pack(side=tk.LEFT, padx=2)

        ttk.Label(toolbar, text="頁面:").pack(side=tk.LEFT, padx=5)
        self.page_var = tk.StringVar(value="0")
        self.page_entry = ttk.Entry(toolbar, textvariable=self.page_var, width=3)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        self.page_entry.bind('<Return>', self.goto_page)

        self.page_label = ttk.Label(toolbar, text="/ 0")
        self.page_label.pack(side=tk.LEFT, padx=2)

        ttk.Button(toolbar, text="上一頁", command=self.prev_page, width=6).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="下一頁", command=self.next_page, width=6).pack(side=tk.LEFT, padx=2)

        ttk.Label(toolbar, text="縮放:").pack(side=tk.LEFT, padx=5)
        self.zoom_var = tk.StringVar(value="100")
        zoom_entry = ttk.Entry(toolbar, textvariable=self.zoom_var, width=6)
        zoom_entry.pack(side=tk.LEFT, padx=2)
        zoom_entry.bind('<Return>', self.set_zoom)

        ttk.Label(toolbar, text="%").pack(side=tk.LEFT)

        ttk.Button(toolbar, text="新增框", command=self.add_text_box, width=6).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="刪除框", command=self.delete_all_text_boxes, width=6).pack(side=tk.LEFT, padx=2)

        self.proofreading_button = ttk.Button(toolbar, text="校對模式: 關", command=self.toggle_proofreading_mode)
        self.proofreading_button.pack(side=tk.LEFT, padx=2)

        self.auto_space_button = ttk.Button(toolbar, text="移除空格: 開", command=self.toggle_auto_remove_spaces)
        self.auto_space_button.pack(side=tk.LEFT, padx=2)

        self.scroll_follow_button = ttk.Button(toolbar, text="邊界滾動: 開", command=self.toggle_scroll_follow)
        self.scroll_follow_button.pack(side=tk.LEFT, padx=2)

        self.textbox_scroll_button = ttk.Button(toolbar, text="文字框滾動: 關", command=self.toggle_textbox_scroll)
        self.textbox_scroll_button.pack(side=tk.LEFT, padx=2)



        self.undo_button = ttk.Button(toolbar, text="◀", command=self.undo_last_action, state='disabled', width=6)
        self.undo_button.pack(side=tk.LEFT, padx=1)

        self.redo_button = ttk.Button(toolbar, text="▶", command=self.redo_last_action, state='disabled', width=6)
        self.redo_button.pack(side=tk.LEFT, padx=1)

        ttk.Button(toolbar, text="重置Y座標", command=self.reset_all_textboxes_to_initial_y).pack(side=tk.LEFT, padx=2)

        ttk.Label(toolbar, text="移動距離:").pack(side=tk.LEFT, padx=5)
        self.move_distance_var = tk.StringVar(value="20")
        move_distance_entry = ttk.Entry(toolbar, textvariable=self.move_distance_var, width=3)
        move_distance_entry.pack(side=tk.LEFT, padx=2)
        ttk.Label(toolbar, text="px").pack(side=tk.LEFT)

        # 字體大小1輸入框
        ttk.Label(toolbar, text="字體大小1:").pack(side=tk.LEFT, padx=5)
        font_size_entry = ttk.Entry(toolbar, textvariable=self.font_size_var, width=3)
        font_size_entry.pack(side=tk.LEFT, padx=2)
        font_size_entry.bind('<Return>', self.update_font)

        # 字體大小2輸入框
        ttk.Label(toolbar, text="字體大小2:").pack(side=tk.LEFT, padx=5)
        font_size_entry2 = ttk.Entry(toolbar, textvariable=self.font_size_var2, width=3)
        font_size_entry2.pack(side=tk.LEFT, padx=2)
        font_size_entry2.bind('<Return>', self.update_font)

        ttk.Label(toolbar, text="紅色字索引:").pack(side=tk.LEFT, padx=5)
        self.red_char_var = tk.StringVar(value="")
        red_char_entry = ttk.Entry(toolbar, textvariable=self.red_char_var, width=3)
        red_char_entry.pack(side=tk.LEFT, padx=2)
        red_char_entry.bind('<Return>', self.set_red_char)

        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.canvas = tk.Canvas(main_frame, bg='white')
        v_scrollbar = ttk.Scrollbar(main_frame, orient='vertical', command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(main_frame, orient='horizontal', command=self.canvas.xview)

        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        self.canvas.bind('<Button-4>', self.on_mousewheel)
        self.canvas.bind('<Button-5>', self.on_mousewheel)
        self.canvas.bind('<Control-MouseWheel>', self.on_zoom_mousewheel)
        self.canvas.bind('<Control-Button-4>', self.on_zoom_mousewheel)
        self.canvas.bind('<Control-Button-5>', self.on_zoom_mousewheel)
        self.canvas.bind('<Double-Button-1>', self.add_text_box_at_position)
        self.canvas.bind('<Button-3>', self.toggle_proofreading_mode)  # 綁定右鍵到切換校對模式
        self.root.bind('<Alt_L>', self.switch_font_var) # 綁定 Alt 鍵到切換字體

        self.root.bind('<Control-z>', lambda e: self.undo_last_action())
        self.root.bind('<Control-Z>', lambda e: self.undo_last_action())
        self.root.bind('<Control-y>', lambda e: self.redo_last_action())
        self.root.bind('<Control-Y>', lambda e: self.redo_last_action())

        self.status_bar = ttk.Label(self.root, text=f"請開啟PDF檔案 | 當前字體大小: {self.font_size_var.get()} | 提示：右鍵切換字體大小")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def add_undo_record(self, record):
        self.undo_history.append(record)
        if len(self.undo_history) > self.max_undo_history:
            self.undo_history.pop(0)

        # 清空重做歷史（因為有新操作）
        self.redo_history.clear()

        # 更新按鈕狀態
        self.undo_button.config(state='normal')
        self.redo_button.config(state='disabled')

        record_count = len(self.undo_history)
        action_name = record.get('action', '未知操作')
        self.status_bar.config(text=f"已記錄操作: {action_name} (可復原操作: {record_count})")

    def undo_last_action(self):
        if not self.undo_history:
            messagebox.showinfo("訊息", "沒有可復原的操作")
            return
        try:
            last_record = self.undo_history.pop()

            # 將操作添加到重做歷史
            self.redo_history.append(last_record)

            if last_record['action'] == 'proofreading_delete':
                textbox = last_record['textbox']
                original_text = last_record['original_text']
                original_x = last_record['original_x']
                original_y = last_record['original_y']
                red_char_index = last_record.get('red_char_index')
                blue_char_info = last_record.get('blue_char_info')
                textbox.restore_state(original_text, original_x, original_y, red_char_index, blue_char_info)
                # 復原後讓畫布滾動跟隨到文字框位置
                self.scroll_to_textbox(textbox)
                self.status_bar.config(text="已復原校對刪除操作")
            elif last_record['action'] == 'set_red_char':
                textbox = last_record['textbox']
                old_index = last_record['old_red_char_index']
                textbox.set_red_char(old_index)
                self.status_bar.config(text="已復原紅色字設置")
            elif last_record['action'] == 'text_edit':
                textbox = last_record['textbox']
                original_text = last_record['original_text']
                original_x = last_record['original_x']
                original_y = last_record['original_y']
                width = last_record.get('width', 200)
                height = last_record.get('height', 100)
                font_size = last_record.get('font_size', 12)
                red_char_index = last_record.get('red_char_index')
                blue_char_info = last_record.get('blue_char_info')
                record_page = last_record.get('page')
                textbox_id = last_record.get('textbox_id')

                # 檢查是否需要切換頁面
                if record_page is not None and record_page != self.current_page:
                    # 保存當前頁面滾動狀態
                    self.save_current_page_scroll()
                    # 切換到記錄所在的頁面
                    self.current_page = record_page
                    self.page_var.set(str(record_page + 1))
                    self.display_page(reset_textboxes=False)
                    # 恢復頁面滾動位置
                    self.restore_page_scroll()

                # 恢復全局文字框狀態
                restored = False
                if self.global_textbox and textbox == self.global_textbox:
                    try:
                        if self.global_textbox.restore_state(original_text, original_x, original_y, red_char_index, blue_char_info):
                            self.global_textbox.last_recorded_text = original_text
                            # 復原後讓畫布滾動跟隨到文字框位置
                            self.scroll_to_textbox(self.global_textbox)
                            restored = True
                    except Exception as e:
                        print(f"恢復全局文字框狀態錯誤: {e}")

                # 如果沒有全局文字框或恢復失敗，創建新的全局文字框
                if not restored:
                    # 創建新的全局文字框
                    self.global_textbox = ResizableTextBox(
                        self.canvas, original_x, original_y, width, height,
                        proofreading_mode=lambda: self.proofreading_mode,
                        move_distance_func=self.get_move_distance,
                        add_undo_record=self.add_undo_record,
                        auto_remove_spaces_func=lambda: self.auto_remove_spaces,
                        page_ref=self
                    )
                    self.global_textbox.set_font("標楷體", font_size)
                    self.global_textbox.text_widget.delete("1.0", "end")
                    self.global_textbox.text_widget.insert("1.0", original_text)
                    self.global_textbox.red_char_index = red_char_index
                    self.global_textbox.update_red_char()

                    # 恢復藍色字符標記
                    if blue_char_info:
                        for start_pos, end_pos in blue_char_info:
                            try:
                                self.global_textbox.text_widget.tag_add("blue_char", start_pos, end_pos)
                            except:
                                pass

                    self.global_textbox.last_recorded_text = original_text
                    self.textbox_page = record_page if record_page is not None else self.current_page
                    # 復原後讓畫布滾動跟隨到文字框位置
                    self.scroll_to_textbox(self.global_textbox)
                    restored = True

                if restored:
                    if record_page is not None and record_page != self.current_page:
                        self.status_bar.config(text=f"已復原文字編輯並切換到第 {record_page + 1} 頁")
                    else:
                        self.status_bar.config(text="已復原文字編輯")
                else:
                    self.status_bar.config(text="無法復原文字編輯：文字框已不存在")
            elif last_record['action'] == 'textbox_page_change':
                # 復原文字框頁面切換
                old_page = last_record['old_page']
                old_textbox_page = last_record['old_textbox_page']



                # 恢復文字框狀態
                if self.global_textbox:
                    self.global_textbox.x = last_record['textbox_x']
                    self.global_textbox.y = last_record['textbox_y']
                    self.global_textbox.width = last_record['textbox_width']
                    self.global_textbox.height = last_record['textbox_height']
                    self.global_textbox.text_widget.delete("1.0", "end")
                    self.global_textbox.text_widget.insert("1.0", last_record['textbox_text'])
                    self.global_textbox.set_font("標楷體", last_record['textbox_font_size'])
                    self.global_textbox.red_char_index = last_record['textbox_red_char_index']
                    self.global_textbox.update_red_char()
                    # 更新文字框位置
                    self.global_textbox.canvas.coords(self.global_textbox.window_id, self.global_textbox.x, self.global_textbox.y)

                # 保存當前頁面的滾動位置
                self.save_current_page_scroll()
                # 切換到舊頁面
                self.current_page = old_page
                self.page_var.set(str(old_page + 1))
                self.textbox_page = old_textbox_page
                self.display_page(reset_textboxes=False)
                # 恢復頁面滾動位置
                self.restore_page_scroll()
                self.status_bar.config(text=f"已復原到第 {old_page + 1} 頁，文字框位於第 {old_textbox_page + 1} 頁")

            elif last_record['action'] == 'textbox_reset_position':
                # 復原文字框位置重置
                if self.global_textbox and self.global_textbox == last_record['textbox']:
                    old_x, old_y = last_record['old_x'], last_record['old_y']
                    # 恢復到重置前的位置
                    self.global_textbox.x = old_x
                    self.global_textbox.y = old_y
                    # 更新畫布上的位置
                    self.global_textbox.canvas.coords(self.global_textbox.window_id, self.global_textbox.x, self.global_textbox.y)
                    # 滾動到文字框位置
                    self.scroll_to_textbox(self.global_textbox)
                    self.status_bar.config(text="已復原文字框位置重置")
                else:
                    self.status_bar.config(text="無法復原文字框位置重置：文字框已不存在")

            elif last_record['action'] == 'textbox_move':
                # 復原文字框移動
                if self.global_textbox and self.global_textbox == last_record['textbox']:
                    old_x, old_y = last_record['old_x'], last_record['old_y']
                    # 恢復到移動前的位置
                    self.global_textbox.x = old_x
                    self.global_textbox.y = old_y
                    # 更新畫布上的位置
                    self.global_textbox.canvas.coords(self.global_textbox.window_id, self.global_textbox.x, self.global_textbox.y)
                    # 滾動到文字框位置
                    self.scroll_to_textbox(self.global_textbox)
                    self.status_bar.config(text="已復原文字框移動")
                else:
                    self.status_bar.config(text="無法復原文字框移動：文字框已不存在")

            # 更新按鈕狀態
            if not self.undo_history:
                self.undo_button.config(state='disabled')
            self.redo_button.config(state='normal')

            record_count = len(self.undo_history)
            redo_count = len(self.redo_history)
            if record_count > 0:
                self.status_bar.config(text=f"復原成功 (可復原: {record_count}, 可重做: {redo_count})")
            else:
                self.status_bar.config(text=f"復原成功 (可重做: {redo_count})")

        except Exception as e:
            messagebox.showerror("錯誤", f"復原操作失敗: {str(e)}")
            if 'last_record' in locals():
                self.undo_history.append(last_record)
                if last_record in self.redo_history:
                    self.redo_history.remove(last_record)

    def redo_last_action(self):
        if not self.redo_history:
            messagebox.showinfo("訊息", "沒有可重做的操作")
            return
        try:
            last_record = self.redo_history.pop()

            if last_record['action'] == 'proofreading_delete':
                textbox = last_record['textbox']
                # 重做刪除操作
                deleted_text = last_record['deleted_text']
                remaining_text = last_record['remaining_text']
                click_index = last_record['click_index']

                # 恢復到刪除前的狀態，然後執行刪除
                textbox.text_widget.delete("1.0", "end")
                textbox.text_widget.insert("1.0", last_record['original_text'])
                textbox.text_widget.delete("1.0", click_index)

                # 移動文字框
                textbox.x = last_record['original_x']
                textbox.y = last_record['original_y']
                textbox.move_textbox_down()

                # 重做後讓畫布滾動跟隨到文字框位置
                self.scroll_to_textbox(textbox)
                self.status_bar.config(text="已重做校對刪除操作")
            elif last_record['action'] == 'set_red_char':
                textbox = last_record['textbox']
                new_index = last_record['new_red_char_index']
                textbox.set_red_char(new_index)
                self.status_bar.config(text="已重做紅色字設置")
            elif last_record['action'] == 'text_edit':
                textbox = last_record['textbox']
                original_text = last_record['original_text']
                original_x = last_record['original_x']
                original_y = last_record['original_y']
                width = last_record.get('width', 200)
                height = last_record.get('height', 100)
                font_size = last_record.get('font_size', 12)
                red_char_index = last_record.get('red_char_index')
                blue_char_info = last_record.get('blue_char_info')
                record_page = last_record.get('page')
                textbox_id = last_record.get('textbox_id')

                # 檢查是否需要切換頁面
                if record_page is not None and record_page != self.current_page:
                    # 保存當前頁面滾動狀態
                    self.save_current_page_scroll()
                    # 切換到記錄所在的頁面
                    self.current_page = record_page
                    self.page_var.set(str(record_page + 1))
                    self.display_page(reset_textboxes=False)
                    # 恢復頁面滾動位置
                    self.restore_page_scroll()

                # 執行重做：使用全局文字框
                if self.global_textbox:
                    try:
                        current_text = self.global_textbox.text_widget.get("1.0", "end-1c")
                        self.global_textbox.last_recorded_text = current_text
                        # 重做後讓畫布滾動跟隨到文字框位置
                        self.scroll_to_textbox(self.global_textbox)
                        self.status_bar.config(text="已重做文字編輯")
                    except Exception as e:
                        print(f"重做文字編輯錯誤: {e}")
                        self.status_bar.config(text="重做文字編輯時發生錯誤")
                else:
                    self.status_bar.config(text="無法重做文字編輯：沒有全局文字框")
            elif last_record['action'] == 'textbox_page_change':
                # 重做文字框頁面切換
                new_page = last_record['new_page']

                # 保存當前頁面的滾動位置
                self.save_current_page_scroll()
                # 切換到新頁面
                self.current_page = new_page
                self.page_var.set(str(new_page + 1))
                self.textbox_page = new_page  # 文字框跟隨到新頁面
                self.display_page(reset_textboxes=False)
                # 恢復頁面滾動位置
                self.restore_page_scroll()
                self.status_bar.config(text=f"已重做到第 {new_page + 1} 頁")

            elif last_record['action'] == 'textbox_reset_position':
                # 重做文字框位置重置
                if self.global_textbox and self.global_textbox == last_record['textbox']:
                    # 重做到重置後的位置
                    self.global_textbox.x = last_record['new_x']
                    self.global_textbox.y = last_record['new_y']
                    # 更新畫布上的位置
                    self.global_textbox.canvas.coords(self.global_textbox.window_id, self.global_textbox.x, self.global_textbox.y)
                    # 滾動到文字框位置
                    self.scroll_to_textbox(self.global_textbox)
                    self.status_bar.config(text="已重做文字框位置重置")
                else:
                    self.status_bar.config(text="無法重做文字框位置重置：文字框已不存在")

            elif last_record['action'] == 'textbox_move':
                # 重做文字框移動
                if self.global_textbox and self.global_textbox == last_record['textbox']:
                    # 重做到移動後的位置
                    self.global_textbox.x = last_record['new_x']
                    self.global_textbox.y = last_record['new_y']
                    # 更新畫布上的位置
                    self.global_textbox.canvas.coords(self.global_textbox.window_id, self.global_textbox.x, self.global_textbox.y)
                    # 滾動到文字框位置
                    self.scroll_to_textbox(self.global_textbox)
                    self.status_bar.config(text="已重做文字框移動")
                else:
                    self.status_bar.config(text="無法重做文字框移動：文字框已不存在")

            # 將操作重新添加到復原歷史
            self.undo_history.append(last_record)

            # 更新按鈕狀態
            if not self.redo_history:
                self.redo_button.config(state='disabled')
            self.undo_button.config(state='normal')

            record_count = len(self.undo_history)
            redo_count = len(self.redo_history)
            if redo_count > 0:
                self.status_bar.config(text=f"重做成功 (可復原: {record_count}, 可重做: {redo_count})")
            else:
                self.status_bar.config(text=f"重做成功 (可復原: {record_count})")

        except Exception as e:
            messagebox.showerror("錯誤", f"重做操作失敗: {str(e)}")
            if 'last_record' in locals():
                self.redo_history.append(last_record)

    def switch_font_var(self, event):
        # 切換字體大小變數並立即應用到所有文字框
        if self.active_font_var == self.font_size_var:
            self.active_font_var = self.font_size_var2
            font_size = self.font_size_var2.get()
            font_label = "字體大小2"
        else:
            self.active_font_var = self.font_size_var
            font_size = self.font_size_var.get()
            font_label = "字體大小1"

        try:
            font_size_val = float(font_size)
            if font_size_val <= 0:
                raise ValueError("字體大小必須大於0")
            updated_count = 0
            if self.global_textbox:
                self.global_textbox.set_font("標楷體", font_size_val)
                updated_count = 1
            self.status_bar.config(text=f"已切換到{font_label}: {font_size} | 已更新 {updated_count} 個文字框")
        except ValueError as e:
            self.status_bar.config(text=f"切換{font_label}失敗: {str(e)}")

        return "break"

    def save_current_page_scroll(self):
        """保存當前頁面的滾動位置"""
        try:
            if not self.pdf_document:
                return

            # 只保存滾動位置
            scroll_x = self.canvas.xview()[0]
            scroll_y = self.canvas.yview()[0]

            # 保存到字典中
            self.page_scroll_positions[self.current_page] = {
                'scroll_x': scroll_x,
                'scroll_y': scroll_y
            }

        except Exception as e:
            print(f"保存頁面滾動位置錯誤: {e}")

    def clean_textbox_undo_history(self):
        """清理與已銷毀文字框相關的復原歷史，但保留有效記錄"""
        try:
            # 過濾復原歷史，只移除真正無效的記錄
            filtered_undo_history = []
            for record in self.undo_history:
                if record.get('action') in ['text_edit', 'proofreading_delete', 'set_red_char']:
                    textbox = record.get('textbox')
                    # 檢查文字框是否真的已被銷毀（而不是只是不在當前頁面）
                    try:
                        if hasattr(textbox, 'text_widget') and textbox.text_widget.winfo_exists():
                            # 文字框仍然有效，保留記錄
                            filtered_undo_history.append(record)
                        else:
                            # 文字框已被銷毀，跳過記錄
                            print(f"清理無效的復原記錄: {record.get('action')}")
                    except:
                        # 如果檢查時出現異常，說明文字框已被銷毀
                        print(f"清理無效的復原記錄: {record.get('action')}")
                else:
                    # 非文字框相關的記錄（如頁面切換），直接保留
                    filtered_undo_history.append(record)

            # 過濾重做歷史
            filtered_redo_history = []
            for record in self.redo_history:
                if record.get('action') in ['text_edit', 'proofreading_delete', 'set_red_char']:
                    textbox = record.get('textbox')
                    # 檢查文字框是否真的已被銷毀
                    try:
                        if hasattr(textbox, 'text_widget') and textbox.text_widget.winfo_exists():
                            # 文字框仍然有效，保留記錄
                            filtered_redo_history.append(record)
                        else:
                            # 文字框已被銷毀，跳過記錄
                            print(f"清理無效的重做記錄: {record.get('action')}")
                    except:
                        # 如果檢查時出現異常，說明文字框已被銷毀
                        print(f"清理無效的重做記錄: {record.get('action')}")
                else:
                    # 非文字框相關的記錄（如頁面切換），直接保留
                    filtered_redo_history.append(record)

            # 更新歷史記錄
            self.undo_history = filtered_undo_history
            self.redo_history = filtered_redo_history

            # 更新按鈕狀態
            if not self.undo_history:
                self.undo_button.config(state='disabled')
            else:
                self.undo_button.config(state='normal')

            if not self.redo_history:
                self.redo_button.config(state='disabled')
            else:
                self.redo_button.config(state='normal')

        except Exception as e:
            print(f"清理復原歷史錯誤: {e}")

    def find_textbox_page(self, target_textbox):
        """查找指定文字框所在的頁面"""
        try:
            # 檢查是否是全局文字框
            if target_textbox == self.global_textbox:
                return self.textbox_page

            return None
        except Exception as e:
            print(f"查找文字框頁面錯誤: {e}")
            return None

    def restore_page_scroll(self):
        """恢復當前頁面的滾動位置"""
        try:
            if not self.pdf_document or self.current_page not in self.page_scroll_positions:
                return False

            page_data = self.page_scroll_positions[self.current_page]

            # 恢復滾動位置
            self.canvas.xview_moveto(page_data['scroll_x'])
            self.canvas.yview_moveto(page_data['scroll_y'])

            return True
        except Exception as e:
            print(f"恢復頁面滾動位置錯誤: {e}")
            return False

    def scroll_to_textbox(self, textbox):
        """讓畫布滾動到指定文字框的相對位置"""
        try:
            if not textbox or not hasattr(textbox, 'x') or not hasattr(textbox, 'y'):
                return

            # 取得畫布的可視區域
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 使用畫布的 scrollregion 配置
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                return

            # 解析 scrollregion 字符串 "x1 y1 x2 y2"
            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
            except (ValueError, AttributeError):
                # 如果解析失敗，回退到使用 bbox
                scroll_region = self.canvas.bbox("all")
                if not scroll_region:
                    return
                total_width = scroll_region[2] - scroll_region[0]
                total_height = scroll_region[3] - scroll_region[1]

            # 計算文字框的中心位置
            textbox_center_x = textbox.x + textbox.width / 2
            textbox_center_y = textbox.y + textbox.height / 2

            # 計算目標滾動位置，讓文字框在畫布中心
            target_scroll_x = (textbox_center_x - canvas_width / 2) / total_width
            target_scroll_y = (textbox_center_y - canvas_height / 2) / total_height

            # 限制滾動範圍在 0.0 到 1.0 之間
            target_scroll_x = max(0.0, min(1.0, target_scroll_x))
            target_scroll_y = max(0.0, min(1.0, target_scroll_y))

            # 執行滾動
            self.canvas.xview_moveto(target_scroll_x)
            self.canvas.yview_moveto(target_scroll_y)

        except Exception as e:
            print(f"滾動到文字框錯誤: {e}")

    def open_pdf(self):
        file_path = filedialog.askopenfilename(
            title="選擇PDF檔案",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.pdf_document = fitz.open(file_path)
                self.current_page = 0
                self.page_var.set("1")
                self.page_label.config(text=f"/ {len(self.pdf_document)}")
                self.zoom_level = 1.0
                self.zoom_var.set("100")
                self.display_page()
                self.undo_history.clear()
                self.redo_history.clear()
                self.undo_button.config(state='disabled')
                self.redo_button.config(state='disabled')
                # 清空頁面滾動位置記錄
                self.page_scroll_positions.clear()
                # 重置全局文字框
                self.global_textbox = None
                self.textbox_page = 0
                self.status_bar.config(text=f"已開啟: {file_path} | 當前字體大小: {self.active_font_var.get()}")
            except Exception as e:
                messagebox.showerror("錯誤", f"無法開啟PDF檔案: {str(e)}")

    def display_page(self, reset_textboxes=True):
        if not self.pdf_document:
            return
        try:
            page = self.pdf_document[self.current_page]
            mat = fitz.Matrix(self.zoom_level, self.zoom_level)
            pix = page.get_pixmap(matrix=mat)

            img_data = pix.tobytes("ppm")
            image = Image.open(io.BytesIO(img_data))

            self.photo = ImageTk.PhotoImage(image)

            self.canvas.delete("pdf_page")
            self.canvas.create_image(0, 0, anchor='nw', image=self.photo, tags="pdf_page")

            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            # 只有在需要時才重置全局文字框到初始位置
            if reset_textboxes and self.global_textbox:
                self.global_textbox.reset_to_initial_position()

        except Exception as e:
            messagebox.showerror("錯誤", f"無法顯示頁面: {str(e)}")

    def prev_page(self):
        if self.pdf_document and self.current_page > 0:
            # 記錄頁面切換操作和文字框狀態
            old_page = self.current_page
            new_page = self.current_page - 1

            # 保存當前頁面的滾動位置
            self.save_current_page_scroll()

            # 記錄文字框狀態變化到復原歷史（無論文字框是否有內容都記錄）
            if self.global_textbox:
                textbox_text = self.global_textbox.text_widget.get("1.0", "end-1c")
                undo_record = {
                    'action': 'textbox_page_change',
                    'old_page': old_page,
                    'new_page': new_page,
                    'textbox_x': self.global_textbox.x,
                    'textbox_y': self.global_textbox.y,
                    'textbox_width': self.global_textbox.width,
                    'textbox_height': self.global_textbox.height,
                    'textbox_text': textbox_text,
                    'textbox_font_size': self.global_textbox.current_font_size,
                    'textbox_red_char_index': self.global_textbox.red_char_index,
                    'old_textbox_page': self.textbox_page
                }
                self.add_undo_record(undo_record)
                print(f"DEBUG: 記錄頁面切換 - 文字框內容: '{textbox_text}' (長度: {len(textbox_text)})")

            self.current_page = new_page
            self.page_var.set(str(self.current_page + 1))

            # 顯示新頁面
            self.display_page(reset_textboxes=False)

            # 讓文字框回到初始位置並記錄這個操作
            if self.global_textbox:
                # 記錄文字框回到初始位置前的狀態
                old_x = self.global_textbox.x
                old_y = self.global_textbox.y

                # 執行回到初始位置
                self.global_textbox.reset_to_initial_position()

                # 記錄這個位置變化到復原歷史
                undo_record = {
                    'action': 'textbox_reset_position',
                    'textbox': self.global_textbox,
                    'old_x': old_x,
                    'old_y': old_y,
                    'new_x': self.global_textbox.x,
                    'new_y': self.global_textbox.y,
                    'page': self.current_page
                }
                self.add_undo_record(undo_record)
                print(f"DEBUG: 記錄位置重置 (prev) - 從 ({old_x}, {old_y}) 到 ({self.global_textbox.x}, {self.global_textbox.y})")

                # 智能滾動到文字框位置
                self.smart_scroll_to_textboxes()
            else:
                # 恢復滾動位置
                self.restore_page_scroll()

    def next_page(self):
        if self.pdf_document and self.current_page < len(self.pdf_document) - 1:
            # 記錄頁面切換操作和文字框狀態
            old_page = self.current_page
            new_page = self.current_page + 1

            # 保存當前頁面的滾動位置
            self.save_current_page_scroll()

            # 記錄文字框狀態變化到復原歷史（無論文字框是否有內容都記錄）
            if self.global_textbox:
                textbox_text = self.global_textbox.text_widget.get("1.0", "end-1c")
                undo_record = {
                    'action': 'textbox_page_change',
                    'old_page': old_page,
                    'new_page': new_page,
                    'textbox_x': self.global_textbox.x,
                    'textbox_y': self.global_textbox.y,
                    'textbox_width': self.global_textbox.width,
                    'textbox_height': self.global_textbox.height,
                    'textbox_text': textbox_text,
                    'textbox_font_size': self.global_textbox.current_font_size,
                    'textbox_red_char_index': self.global_textbox.red_char_index,
                    'old_textbox_page': self.textbox_page
                }
                self.add_undo_record(undo_record)

            self.current_page = new_page
            self.page_var.set(str(self.current_page + 1))

            # 顯示新頁面
            self.display_page(reset_textboxes=False)

            # 讓文字框回到初始位置並記錄這個操作
            if self.global_textbox:
                # 記錄文字框回到初始位置前的狀態
                old_x = self.global_textbox.x
                old_y = self.global_textbox.y

                # 執行回到初始位置
                self.global_textbox.reset_to_initial_position()

                # 記錄這個位置變化到復原歷史
                undo_record = {
                    'action': 'textbox_reset_position',
                    'textbox': self.global_textbox,
                    'old_x': old_x,
                    'old_y': old_y,
                    'new_x': self.global_textbox.x,
                    'new_y': self.global_textbox.y,
                    'page': self.current_page
                }
                self.add_undo_record(undo_record)

                # 智能滾動到文字框位置
                self.smart_scroll_to_textboxes()
            else:
                # 恢復滾動位置
                self.restore_page_scroll()

    def goto_page(self, event=None):
        if not self.pdf_document:
            return
        try:
            page_num = int(self.page_var.get()) - 1
            if 0 <= page_num < len(self.pdf_document):
                # 記錄頁面切換操作
                old_page = self.current_page
                new_page = page_num

                # 只有在頁面真的改變時才記錄
                if old_page != new_page:
                    # 保存當前頁面的滾動位置
                    self.save_current_page_scroll()

                    # 記錄文字框狀態變化到復原歷史
                    if self.global_textbox:
                        undo_record = {
                            'action': 'textbox_page_change',
                            'old_page': old_page,
                            'new_page': new_page,
                            'textbox_x': self.global_textbox.x,
                            'textbox_y': self.global_textbox.y,
                            'textbox_width': self.global_textbox.width,
                            'textbox_height': self.global_textbox.height,
                            'textbox_text': self.global_textbox.text_widget.get("1.0", "end-1c"),
                            'textbox_font_size': self.global_textbox.current_font_size,
                            'textbox_red_char_index': self.global_textbox.red_char_index,
                            'old_textbox_page': self.textbox_page
                        }
                        self.add_undo_record(undo_record)

                    self.current_page = page_num

                    # 顯示新頁面
                    self.display_page(reset_textboxes=False)

                    # 讓文字框回到初始位置並記錄這個操作
                    if self.global_textbox:
                        # 記錄文字框回到初始位置前的狀態
                        old_x = self.global_textbox.x
                        old_y = self.global_textbox.y

                        # 執行回到初始位置
                        self.global_textbox.reset_to_initial_position()

                        # 記錄這個位置變化到復原歷史
                        undo_record = {
                            'action': 'textbox_reset_position',
                            'textbox': self.global_textbox,
                            'old_x': old_x,
                            'old_y': old_y,
                            'new_x': self.global_textbox.x,
                            'new_y': self.global_textbox.y,
                            'page': self.current_page
                        }
                        self.add_undo_record(undo_record)

                        # 智能滾動到文字框位置
                        self.smart_scroll_to_textboxes()
                    else:
                        # 恢復滾動位置
                        self.restore_page_scroll()
            else:
                messagebox.showwarning("警告", "頁面號碼超出範圍")
                self.page_var.set(str(self.current_page + 1))
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的頁面號碼")
            self.page_var.set(str(self.current_page + 1))

    def set_zoom(self, event=None):
        try:
            zoom_percent = float(self.zoom_var.get())
            if zoom_percent > 0:
                self.zoom_level = zoom_percent / 100.0
                self.display_page(reset_textboxes=False)  # 縮放時不重置文字框位置
                # 格式化顯示：如果是整數就不顯示小數點，否則顯示小數點
                if zoom_percent == int(zoom_percent):
                    zoom_display = f"{int(zoom_percent)}"
                else:
                    zoom_display = f"{zoom_percent:.1f}"
                self.status_bar.config(text=f"縮放已設定為 {zoom_display}% | 當前字體大小: {self.active_font_var.get()}")
            else:
                messagebox.showwarning("警告", "縮放比例必須大於0")
                # 恢復時也支持小數點顯示
                current_zoom = self.zoom_level * 100
                if current_zoom == int(current_zoom):
                    self.zoom_var.set(str(int(current_zoom)))
                else:
                    self.zoom_var.set(f"{current_zoom:.1f}")
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的縮放比例")
            # 恢復時也支持小數點顯示
            current_zoom = self.zoom_level * 100
            if current_zoom == int(current_zoom):
                self.zoom_var.set(str(int(current_zoom)))
            else:
                self.zoom_var.set(f"{current_zoom:.1f}")

    def on_mousewheel(self, event):
        # 畫布滾動在校對模式下仍然可用
        if event.num == 4 or event.delta > 0:
            self.canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:
            self.canvas.yview_scroll(1, "units")

    def on_zoom_mousewheel(self, event):
        """處理 Ctrl + 滾輪的縮放功能，支援小數點縮放"""
        if not self.pdf_document:
            return

        try:
            current_zoom = self.zoom_level * 100

            # 設定縮放步長為0.1%，實現精細縮放控制
            zoom_step = 0.1

            if event.num == 4 or event.delta > 0:
                # 向上滾動，放大
                new_zoom = current_zoom + zoom_step
            elif event.num == 5 or event.delta < 0:
                # 向下滾動，縮小
                new_zoom = current_zoom - zoom_step
            else:
                return

            # 限制縮放範圍在10%到500%之間
            new_zoom = max(10.0, min(500.0, new_zoom))

            # 四捨五入到一位小數，避免浮點數精度問題
            new_zoom = round(new_zoom, 1)

            # 更新縮放
            self.zoom_level = new_zoom / 100.0

            # 格式化顯示：如果是整數就不顯示小數點，否則顯示一位小數
            if new_zoom == int(new_zoom):
                zoom_display = str(int(new_zoom))
                self.zoom_var.set(str(int(new_zoom)))
            else:
                zoom_display = f"{new_zoom:.1f}"
                self.zoom_var.set(f"{new_zoom:.1f}")

            self.display_page(reset_textboxes=False)  # 縮放時不重置文字框位置
            self.status_bar.config(text=f"縮放已設定為 {zoom_display}% | 當前字體大小: {self.active_font_var.get()}")

        except Exception as e:
            print(f"縮放錯誤: {e}")

    def add_text_box(self):
        # 如果已經有全局文字框，不創建新的
        if self.global_textbox:
            return

        x = 50
        y = 50
        self.global_textbox = ResizableTextBox(
            self.canvas, x, y,
            proofreading_mode=lambda: self.proofreading_mode,
            move_distance_func=self.get_move_distance,
            add_undo_record=self.add_undo_record,
            auto_remove_spaces_func=lambda: self.auto_remove_spaces,
            page_ref=self
        )
        self.global_textbox.set_font("標楷體", self.active_font_var.get())
        self.textbox_page = self.current_page

    def add_text_box_at_position(self, event):
        # 如果已經有全局文字框，不創建新的
        if self.global_textbox:
            return

        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        # 創建全局文字框
        self.global_textbox = ResizableTextBox(
            self.canvas, x, y,
            proofreading_mode=lambda: self.proofreading_mode,
            move_distance_func=self.get_move_distance,
            add_undo_record=self.add_undo_record,
            auto_remove_spaces_func=lambda: self.auto_remove_spaces,
            page_ref=self
        )

        self.global_textbox.set_font("標楷體", self.active_font_var.get())
        self.textbox_page = self.current_page

    def delete_all_text_boxes(self):
        """完全重置應用程式狀態，如同PDF剛開啟時的狀態"""
        # 檢查是否有全局文字框
        if not self.global_textbox:
            messagebox.showinfo("訊息", "目前沒有文字框可以刪除")
            return

        # 詢問用戶確認
        result = messagebox.askyesno(
            "確認重置",
            f"確定要完全重置應用程式狀態嗎？\n"
            f"這將刪除全局文字框並重置所有設定：\n"
            f"• 全局文字框 (位於第 {self.textbox_page + 1} 頁)\n"
            f"• 所有頁面滾動位置記錄\n"
            f"• 復原/重做歷史\n"
            f"此操作無法復原。",
            icon='warning'
        )

        if result:
            try:
                # 0. 先跳回第一頁再執行刪除操作
                if self.pdf_document and self.current_page != 0:
                    # 保存當前頁面的滾動位置
                    self.save_current_page_scroll()
                    # 跳到第一頁
                    self.current_page = 0
                    self.page_var.set("1")
                    # 顯示第一頁
                    self.display_page(reset_textboxes=False)
                    # 嘗試恢復第一頁的滾動位置
                    self.restore_page_scroll()

                # 1. 刪除全局文字框
                if self.global_textbox:
                    try:
                        # 刪除畫布上的文字框元素
                        if hasattr(self.global_textbox, 'window_id'):
                            self.canvas.delete(self.global_textbox.window_id)
                        # 銷毀文字框的 tkinter 元件
                        if hasattr(self.global_textbox, 'frame'):
                            self.global_textbox.frame.destroy()
                    except Exception as e:
                        print(f"刪除全局文字框時發生錯誤: {e}")

                # 2. 清空全局文字框引用
                self.global_textbox = None
                self.textbox_page = 0

                # 3. 完全清空所有頁面的滾動位置記錄
                self.page_scroll_positions.clear()

                # 4. 清空復原和重做歷史
                self.undo_history.clear()
                self.redo_history.clear()
                self.undo_button.config(state='disabled')
                self.redo_button.config(state='disabled')

                # 5. 重置滾動位置到左上角
                self.canvas.yview_moveto(0)
                self.canvas.xview_moveto(0)

                # 6. 更新狀態欄
                self.status_bar.config(text=f"應用程式狀態已完全重置 - 已刪除全局文字框及所有記錄 | 當前字體大小: {self.active_font_var.get()}")

            except Exception as e:
                messagebox.showerror("錯誤", f"重置應用程式狀態時發生錯誤: {str(e)}")
        else:
            self.status_bar.config(text=f"取消重置操作 | 全局文字框位於第 {self.textbox_page + 1} 頁")

    def reset_all_textboxes_to_initial_y(self):
        """重置全局文字框的Y座標到初始位置，保持X座標不變，並根據文字框位置調整滾動"""
        if not self.global_textbox:
            messagebox.showinfo("訊息", "目前沒有文字框可以重置")
            return

        try:
            # 重置全局文字框的Y座標
            self.global_textbox.reset_to_initial_y()
            x_position = self.global_textbox.x

            # 重置垂直卷軸到頂部
            self.canvas.yview_moveto(0)

            # 根據文字框的X位置調整水平滾動
            # 獲取滾動區域信息
            scrollregion = self.canvas.cget("scrollregion")
            if scrollregion:
                try:
                    x1, y1, x2, y2 = map(float, scrollregion.split())
                    total_width = x2 - x1
                    canvas_width = self.canvas.winfo_width()

                    # 計算滾動位置，讓文字框在畫面中央
                    if total_width > canvas_width:
                        target_x = x_position - (canvas_width / 2)
                        scroll_ratio = max(0, min(1, target_x / (total_width - canvas_width)))
                        self.canvas.xview_moveto(scroll_ratio)
                    else:
                        # 如果內容寬度小於畫布寬度，滾動到左邊
                        self.canvas.xview_moveto(0)
                except (ValueError, AttributeError):
                    # 如果解析失敗，滾動到左邊
                    self.canvas.xview_moveto(0)
            else:
                # 如果沒有滾動區域，滾動到左邊
                self.canvas.xview_moveto(0)

            self.status_bar.config(text=f"已重置全局文字框的Y座標到初始位置並調整滾動位置 | 當前字體大小: {self.active_font_var.get()}")

        except Exception as e:
            messagebox.showerror("錯誤", f"重置文字框Y座標時發生錯誤: {str(e)}")

    def smart_scroll_to_textboxes(self):
        """智能滾動到全局文字框位置"""
        if not self.global_textbox:
            # 如果沒有文字框，滾動到左上角
            self.canvas.yview_moveto(0)
            self.canvas.xview_moveto(0)
            return

        try:
            # 獲取全局文字框的位置
            textbox_x = self.global_textbox.x
            textbox_y = self.global_textbox.y

            # 獲取滾動區域信息
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion:
                # 如果沒有滾動區域，滾動到左上角
                self.canvas.yview_moveto(0)
                self.canvas.xview_moveto(0)
                return

            try:
                x1, y1, x2, y2 = map(float, scrollregion.split())
                total_width = x2 - x1
                total_height = y2 - y1
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()

                # 計算水平滾動位置
                if total_width > canvas_width:
                    target_x = textbox_x - (canvas_width / 2)
                    scroll_x_ratio = max(0, min(1, target_x / (total_width - canvas_width)))
                    self.canvas.xview_moveto(scroll_x_ratio)
                else:
                    self.canvas.xview_moveto(0)

                # 計算垂直滾動位置
                if total_height > canvas_height:
                    target_y = textbox_y - (canvas_height / 2)
                    scroll_y_ratio = max(0, min(1, target_y / (total_height - canvas_height)))
                    self.canvas.yview_moveto(scroll_y_ratio)
                else:
                    self.canvas.yview_moveto(0)

            except (ValueError, AttributeError):
                # 如果解析失敗，滾動到左上角
                self.canvas.yview_moveto(0)
                self.canvas.xview_moveto(0)

        except Exception as e:
            print(f"智能滾動錯誤: {e}")
            # 出錯時滾動到左上角
            self.canvas.yview_moveto(0)
            self.canvas.xview_moveto(0)

    def toggle_proofreading_mode(self, event=None):
        self.proofreading_mode = not self.proofreading_mode
        if self.proofreading_mode:
            self.proofreading_button.config(text="校對模式: 開")
            new_color = 'red'
            self.status_bar.config(text=f"校對模式已開啟 | 當前字體大小: {self.active_font_var.get()}")
        else:
            self.proofreading_button.config(text="校對模式: 關")
            new_color = 'gray'
            self.status_bar.config(text=f"校對模式已關閉 | 當前字體大小: {self.active_font_var.get()}")

        if self.global_textbox:
            self.global_textbox.set_border_color(new_color)

    def toggle_auto_remove_spaces(self, event=None):
        """切換自動移除空格功能"""
        self.auto_remove_spaces = not self.auto_remove_spaces
        if self.auto_remove_spaces:
            self.auto_space_button.config(text="移除空格: 開")
            self.status_bar.config(text=f"自動移除空格功能已開啟 | 當前字體大小: {self.active_font_var.get()}")
        else:
            self.auto_space_button.config(text="移除空格: 關")
            self.status_bar.config(text=f"自動移除空格功能已關閉 | 當前字體大小: {self.active_font_var.get()}")

    def toggle_scroll_follow(self, event=None):
        """切換邊界滾動跟隨功能"""
        self.scroll_follow_enabled = not self.scroll_follow_enabled
        if self.scroll_follow_enabled:
            self.scroll_follow_button.config(text="邊界滾動: 開")
            self.status_bar.config(text=f"邊界滾動跟隨功能已開啟 - 只在文字框接近邊界時滾動 | 當前字體大小: {self.active_font_var.get()}")
        else:
            self.scroll_follow_button.config(text="邊界滾動: 關")
            self.status_bar.config(text=f"邊界滾動跟隨功能已關閉 | 當前字體大小: {self.active_font_var.get()}")

    def toggle_textbox_scroll(self):
        """切換文字框滾動功能"""
        self.textbox_scroll_enabled = not self.textbox_scroll_enabled
        if self.textbox_scroll_enabled:
            self.textbox_scroll_button.config(text="文字框滾動: 開")
            self.status_bar.config(text=f"文字框內滾動功能已開啟 | 當前字體大小: {self.active_font_var.get()}")
        else:
            self.textbox_scroll_button.config(text="文字框滾動: 關")
            self.status_bar.config(text=f"文字框內滾動功能已關閉 | 當前字體大小: {self.active_font_var.get()}")


    def get_move_distance(self):
        try:
            distance = float(self.move_distance_var.get())
            return max(0, distance)
        except ValueError:
            return 20

    def update_font(self, event=None):
        try:
            font_size_str = self.active_font_var.get()
            font_size = float(font_size_str)
            if font_size <= 0:
                raise ValueError("字體大小必須大於0")
            updated_count = 0
            if self.global_textbox:
                self.global_textbox.set_font("標楷體", font_size)
                updated_count = 1
            font_label = "字體大小1" if self.active_font_var == self.font_size_var else "字體大小2"
            self.status_bar.config(text=f"已更新 {updated_count} 個文字框的{font_label}為 {font_size}")
        except Exception as e:
            messagebox.showerror("錯誤", f"字體更新失敗: {str(e)}")

    def set_red_char(self, event=None):
        try:
            index_str = self.red_char_var.get()
            if index_str.strip() == "":
                index = None
            else:
                index = int(index_str) - 1
                if index < 0:
                    raise ValueError("索引必須大於0")
            updated_count = 0
            if self.global_textbox:
                self.global_textbox.set_red_char(index)
                updated_count = 1
            if updated_count > 0:
                self.status_bar.config(text=f"已設置 {updated_count} 個文字框的紅色字索引為 {index_str if index is not None else '無'}")
            else:
                self.status_bar.config(text="沒有文字框可設置紅色字")
        except Exception as e:
            messagebox.showerror("錯誤", f"設置紅色字失敗: {str(e)}")

def main():
    root = tk.Tk()
    app = PDFReader(root)
    root.mainloop()

if __name__ == "__main__":
    main()